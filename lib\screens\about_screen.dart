import 'package:flutter/material.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('معلومات عنا'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Company Logo/Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF1976D2), Color(0xFF1565C0)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: const Icon(
                      Icons.waves,
                      size: 50,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Pescados La Costa',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'رائدون في صناعة الأسماك منذ سنوات',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 30),

            // About Us Section
            _buildSectionCard(
              'من نحن',
              'شركة Pescados La Costa هي شركة رائدة في مجال استيراد وتصدير وتحويل الأسماك. تأسست الشركة برؤية واضحة لتوفير أجود أنواع الأسماك الطازجة والمجمدة للأسواق المحلية والدولية.',
              Icons.business,
              const Color(0xFF4CAF50),
            ),

            // Our Mission
            _buildSectionCard(
              'رسالتنا',
              'نسعى لتقديم منتجات بحرية عالية الجودة تلبي احتياجات عملائنا وتفوق توقعاتهم، مع الحفاظ على أعلى معايير الجودة والسلامة الغذائية في جميع مراحل العمل.',
              Icons.flag,
              const Color(0xFF2196F3),
            ),

            // Our Vision
            _buildSectionCard(
              'رؤيتنا',
              'أن نكون الشركة الرائدة في منطقة شمال أفريقيا في مجال تجارة وتحويل الأسماك، ونساهم في تطوير الصناعة البحرية بما يخدم الاقتصاد المحلي والإقليمي.',
              Icons.visibility,
              const Color(0xFFFF9800),
            ),

            // Our Services
            _buildSectionCard(
              'خدماتنا',
              '• استيراد الأسماك الطازجة من أفضل المصادر العالمية\n• تصدير المنتجات البحرية للأسواق الدولية\n• تحويل وتجهيز الأسماك بأحدث التقنيات\n• خدمات التبريد والتخزين المتطورة\n• التوزيع والتوصيل السريع',
              Icons.miscellaneous_services,
              const Color(0xFF9C27B0),
            ),

            // Quality Standards
            _buildSectionCard(
              'معايير الجودة',
              'نلتزم بأعلى معايير الجودة العالمية في جميع عملياتنا. مصانعنا حاصلة على شهادات الجودة الدولية ونطبق نظام HACCP لضمان سلامة الغذاء من المصدر حتى المستهلك.',
              Icons.verified,
              const Color(0xFF8BC34A),
            ),

            // Location
            _buildSectionCard(
              'موقعنا الاستراتيجي',
              'تقع شركتنا في موقع استراتيجي في حي تناولة الجديدة بني - الناظور، مما يوفر لنا سهولة الوصول إلى الموانئ والأسواق المحلية والدولية.',
              Icons.location_on,
              const Color(0xFFE91E63),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, String content, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              content,
              style: const TextStyle(
                fontSize: 15,
                color: Colors.black87,
                height: 1.6,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }
}
