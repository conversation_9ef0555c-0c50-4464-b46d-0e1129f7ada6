import 'package:flutter/material.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pescados La Costa'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.waves,
                    size: 60,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'مرحباً بكم في Pescados La Costa',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'رائدون في استيراد وتصدير وتحويل الأسماك',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 30),
            
            // Articles Section
            const Text(
              'مقالات ومعلومات',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1976D2),
              ),
            ),
            const SizedBox(height: 20),
            
            // Article Cards
            _buildArticleCard(
              context,
              'أهمية الأسماك الطازجة في التغذية',
              'تعتبر الأسماك الطازجة مصدراً غنياً بالبروتينات والأحماض الدهنية الأساسية التي يحتاجها الجسم. في Pescados La Costa، نحرص على توفير أجود أنواع الأسماك الطازجة المستوردة من أفضل المصادر البحرية.',
              Icons.restaurant,
              const Color(0xFF4CAF50),
            ),
            
            _buildArticleCard(
              context,
              'عمليات الاستيراد والتصدير المتطورة',
              'نستخدم أحدث التقنيات في عمليات الاستيراد والتصدير لضمان وصول الأسماك بأفضل حالة. شبكة التبريد المتطورة لدينا تحافظ على جودة المنتجات من المصدر حتى المستهلك.',
              Icons.local_shipping,
              const Color(0xFF2196F3),
            ),
            
            _buildArticleCard(
              context,
              'تحويل وتجهيز الأسماك بمعايير عالمية',
              'مصنعنا مجهز بأحدث المعدات لتحويل وتجهيز الأسماك وفقاً للمعايير الدولية. نقدم خدمات التنظيف والتقطيع والتعبئة بجودة عالية تلبي احتياجات عملائنا.',
              Icons.precision_manufacturing,
              const Color(0xFFFF9800),
            ),
            
            _buildArticleCard(
              context,
              'الاستدامة البحرية ومسؤوليتنا البيئية',
              'نؤمن بأهمية الحفاظ على البيئة البحرية. نعمل مع موردين ملتزمين بممارسات الصيد المستدام ونساهم في حماية الثروة السمكية للأجيال القادمة.',
              Icons.eco,
              const Color(0xFF8BC34A),
            ),
            
            _buildArticleCard(
              context,
              'شبكة التوزيع الواسعة',
              'تغطي شبكة التوزيع لدينا مناطق واسعة لضمان وصول منتجاتنا الطازجة إلى جميع العملاء. نعمل مع شركاء موثوقين لتوفير خدمة توصيل سريعة وآمنة.',
              Icons.network_check,
              const Color(0xFF9C27B0),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArticleCard(BuildContext context, String title, String content, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              content,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }
}
