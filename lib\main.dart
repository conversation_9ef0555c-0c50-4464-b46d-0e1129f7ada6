import 'package:flutter/material.dart';

void main() {
  runApp(const PescadosLaCostaApp());
}

class PescadosLaCostaApp extends StatelessWidget {
  const PescadosLaCostaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Pescados La Costa',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1976D2), // لون أزرق البحر
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1976D2),
          foregroundColor: Colors.white,
          elevation: 2,
        ),
      ),
      home: const MainScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pescados La Costa'),
        centerTitle: true,
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: [
          _buildHomeScreen(),
          _buildAboutScreen(),
          _buildContactScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.info),
            label: 'معلومات عنا',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.contact_phone),
            label: 'اتصل بنا',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: const Color(0xFF1976D2),
        onTap: _onItemTapped,
      ),
    );
  }

  Widget _buildHomeScreen() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.waves,
                  size: 60,
                  color: Colors.white,
                ),
                const SizedBox(height: 10),
                const Text(
                  'مرحباً بكم في Pescados La Costa',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'رائدون في استيراد وتصدير وتحويل الأسماك',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),

          // Articles Section
          const Text(
            'مقالات ومعلومات',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1976D2),
            ),
          ),
          const SizedBox(height: 20),

          // Article Cards
          _buildArticleCard(
            'أهمية الأسماك الطازجة في التغذية',
            'تعتبر الأسماك الطازجة مصدراً غنياً بالبروتينات والأحماض الدهنية الأساسية التي يحتاجها الجسم. في Pescados La Costa، نحرص على توفير أجود أنواع الأسماك الطازجة المستوردة من أفضل المصادر البحرية.',
            Icons.restaurant,
            const Color(0xFF4CAF50),
          ),

          _buildArticleCard(
            'عمليات الاستيراد والتصدير المتطورة',
            'نستخدم أحدث التقنيات في عمليات الاستيراد والتصدير لضمان وصول الأسماك بأفضل حالة. شبكة التبريد المتطورة لدينا تحافظ على جودة المنتجات من المصدر حتى المستهلك.',
            Icons.local_shipping,
            const Color(0xFF2196F3),
          ),

          _buildArticleCard(
            'تحويل وتجهيز الأسماك بمعايير عالمية',
            'مصنعنا مجهز بأحدث المعدات لتحويل وتجهيز الأسماك وفقاً للمعايير الدولية. نقدم خدمات التنظيف والتقطيع والتعبئة بجودة عالية تلبي احتياجات عملائنا.',
            Icons.precision_manufacturing,
            const Color(0xFFFF9800),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutScreen() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Company Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF1976D2), Color(0xFF1565C0)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: const Icon(
                    Icons.waves,
                    size: 50,
                    color: Color(0xFF1976D2),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Pescados La Costa',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'رائدون في صناعة الأسماك منذ سنوات',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),

          _buildSectionCard(
            'من نحن',
            'شركة Pescados La Costa هي شركة رائدة في مجال استيراد وتصدير وتحويل الأسماك. تأسست الشركة برؤية واضحة لتوفير أجود أنواع الأسماك الطازجة والمجمدة للأسواق المحلية والدولية.',
            Icons.business,
            const Color(0xFF4CAF50),
          ),

          _buildSectionCard(
            'رسالتنا',
            'نسعى لتقديم منتجات بحرية عالية الجودة تلبي احتياجات عملائنا وتفوق توقعاتهم، مع الحفاظ على أعلى معايير الجودة والسلامة الغذائية في جميع مراحل العمل.',
            Icons.flag,
            const Color(0xFF2196F3),
          ),

          _buildSectionCard(
            'رؤيتنا',
            'أن نكون الشركة الرائدة في منطقة شمال أفريقيا في مجال تجارة وتحويل الأسماك، ونساهم في تطوير الصناعة البحرية بما يخدم الاقتصاد المحلي والإقليمي.',
            Icons.visibility,
            const Color(0xFFFF9800),
          ),
        ],
      ),
    );
  }

  Widget _buildContactScreen() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.contact_phone,
                  size: 50,
                  color: Colors.white,
                ),
                const SizedBox(height: 16),
                const Text(
                  'تواصل معنا',
                  style: TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'نحن هنا لخدمتكم في أي وقت',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),

          _buildContactCard(
            'العنوان',
            'حي تناولة الجديدة بني - الناظور',
            Icons.location_on,
            const Color(0xFFE91E63),
          ),

          _buildContactCard(
            'رقم الهاتف',
            '0655579785',
            Icons.phone,
            const Color(0xFF4CAF50),
          ),
        ],
      ),
    );
  }

  Widget _buildArticleCard(String title, String content, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              content,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, String content, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              content,
              style: const TextStyle(
                fontSize: 15,
                color: Colors.black87,
                height: 1.6,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard(String title, String content, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    content,
                    style: const TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
